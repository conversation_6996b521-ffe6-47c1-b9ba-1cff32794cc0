<template>
	<farm-box>
		<farm-row no-default-gutters class="main-layout">
			<farm-col cols="3" class="campaigns-sidebar">
				<farm-bodytext type="1" variation="bold" color="primary" class="mb-4">
					Campanhas Filtradas
				</farm-bodytext>
				<div class="campaign-card">
					<farm-typography size="md" weight="500" >
						Texto do retângulo
					</farm-typography>
					<farm-switcher
						:value="true"
						@input="(value) => console.log('Switcher changed:', value)"
					/>
				</div>
			</farm-col>

			<farm-col cols="9" no-gutters class="content-area">
				<farm-row no-default-gutters class="content-layout">
					<farm-col cols="12" class="filters-section">
						<farm-bodytext type="1" variation="bold"  class="mb-4">
							Preencha os campos abaixo para popular a timeline:
						</farm-bodytext>

						<div class="filters-container">
							<div class="filter-group commercial-product-group">
								<farm-label for="commercial-product">Produto Comercial</farm-label>
								<farm-select
									id="commercial-product"
									class="commercial-product-select"
									v-model="formFilters.commercial_prd_id"
									item-text="text"
									item-value="value"
									:items="commercialProductsForSelect"
								/>
							</div>
							<div class="filter-group">
								<farm-label for="operation-type">Tipo de Operação</farm-label>
								<farm-select
									id="operation-type"
									v-model="formFilters.operation_type"
									item-text="text"
									item-value="value"
									:items="operationTypesForSelect"
								/>
							</div>
							<div class="filter-group">
								<farm-label for="campaign-status">Status da Campanha</farm-label>
								<farm-select
									id="campaign-status"
									v-model="formFilters.campaign_status"
									:items="FORM_STATUSES"
								/>
							</div>
							<div class="buttons-group">
								<farm-btn outlined @click="aplicarFiltros">
									Aplicar Filtros
								</farm-btn>
								<farm-btn plain @click="limparFiltros">
									Limpar Filtros
								</farm-btn>
							</div>
						</div>
					</farm-col>

					<farm-col cols="12" class="chart-section">
						<farm-bodytext type="1" variation="bold" class="mb-4">
							Gráfico
						</farm-bodytext>

						<div v-if="isLoading" class="chart-loading">
							<farm-typography size="md" color="black" color-variation="50">
								Carregando dados do gráfico...
							</farm-typography>
						</div>

						<div v-else-if="ganttChartData && ganttChartData.groups.length > 0" class="chart-container">
							<farm-gantt-chart :data="ganttChartData" />
						</div>

						<div v-else-if="hasGanttDataError" class="chart-error">
							<farm-typography size="md" color="error">
								Erro ao processar dados do gráfico. Verifique os filtros e tente novamente.
							</farm-typography>
						</div>

						<div v-else-if="campaignTimeline && campaignTimeline.length === 0" class="chart-empty">
							<farm-typography size="md" color="black" color-variation="50">
								Nenhum dado encontrado. Aplique os filtros para visualizar o gráfico.
							</farm-typography>
						</div>

						<div v-else-if="!isFormValid" class="chart-placeholder">
							<farm-typography size="md" color="black" color-variation="50">
								Preencha todos os filtros obrigatórios para visualizar o gráfico.
							</farm-typography>
						</div>
					</farm-col>
				</farm-row>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onBeforeMount, computed } from 'vue';
import { Switcher } from '@farm-investimentos/front-mfe-components/src/components/Switcher';
import { useIsLoading } from '@/composibles';
import { OPERATION_TYPES } from '@/constants';
import { useCampaign } from '../../composables';
import { FORM_STATUSES } from '../../constants';
import type { CampaignTimelineItem } from '../../types';
import type { GanttData } from '@farm-investimentos/front-mfe-components/src/components/GanttChart/types';

interface CampaignData {
	id: number;
	name: string;
	isActive: boolean;
}

export default defineComponent({
	name: 'TabCustomFilter',
	components: {
		'farm-switcher': Switcher
	},
	setup() {
		const {
			campaignTimeline,
			campaignTimelineRequestStatus,
			commercialProductsList,
			commercialProductsListRequestStatus,
			fetchCommercialProducts,
			fetchCampaignTimeline,
		} = useCampaign();


		const campaigns = ref<CampaignData[]>([
			{ id: 1, name: 'Campanha Black Friday', isActive: true },
			{ id: 2, name: 'Campanha Natal 2024', isActive: false },
			{ id: 3, name: 'Campanha Ano Novo', isActive: true },
			{ id: 4, name: 'Campanha Carnaval', isActive: false }
		]);

		const formFilters = ref({
			commercial_prd_id: null,
			operation_type: null,
			campaign_status: [],
		});

		const isLoading = useIsLoading([
			campaignTimelineRequestStatus,
			commercialProductsListRequestStatus,
		]);

		/**
		 * Transforms campaign timeline data into Gantt chart format
		 * @param timelineData - Array of CampaignTimelineItem from API
		 * @returns GanttData formatted for the GanttChart component
		 */
		const transformToGanttData = (timelineData: CampaignTimelineItem[]): GanttData => {
			if (!timelineData || !Array.isArray(timelineData) || timelineData.length === 0) {
				return { groups: [] };
			}

			const groups = timelineData.map((item, index) => {
				try {
					const { campaign, commercialProduct } = item;

					// Validate required data
					if (!campaign || !commercialProduct) {
						console.warn(`GanttChart: Invalid campaign timeline item at index ${index} - missing campaign or commercialProduct`, item);
						return null;
					}

					// Validate required fields
					if (!campaign.name || !campaign.id) {
						console.warn(`GanttChart: Invalid campaign data at index ${index} - missing name or id`, campaign);
						return null;
					}

					if (!commercialProduct.name || !commercialProduct.id) {
						console.warn(`GanttChart: Invalid commercial product data at index ${index} - missing name or id`, commercialProduct);
						return null;
					}

					// Helper function to validate and parse dates
					const parseDate = (dateStr: string, fieldName: string): Date | null => {
						if (!dateStr) {
							console.warn(`GanttChart: Missing date for ${fieldName} at index ${index}`);
							return null;
						}
						const date = new Date(dateStr);
						if (isNaN(date.getTime())) {
							console.warn(`GanttChart: Invalid date format for ${fieldName} at index ${index}: ${dateStr}`);
							return null;
						}
						return date;
					};

					// Parse and validate all dates
					const campaignStart = parseDate(campaign.startDate, 'campaign.startDate');
					const campaignEnd = parseDate(campaign.endDate, 'campaign.endDate');
					const productStart = parseDate(commercialProduct.startDate, 'commercialProduct.startDate');
					const productEnd = parseDate(commercialProduct.endDate, 'commercialProduct.endDate');
					const disbursementStart = parseDate(commercialProduct.startDisbursementDate, 'commercialProduct.startDisbursementDate');
					const disbursementEnd = parseDate(commercialProduct.endDisbursementDate, 'commercialProduct.endDisbursementDate');
					const dueStart = parseDate(commercialProduct.startDueDate, 'commercialProduct.startDueDate');
					const dueEnd = parseDate(commercialProduct.endDueDate, 'commercialProduct.endDueDate');

					// Helper function to create a bar with validation
					const createBar = (id: string, label: string, start: Date | null, end: Date | null, color: string, tooltipData: any) => {
						if (!start || !end) return null;

						// Ensure end date is not before start date
						if (end < start) {
							console.warn(`GanttChart: End date before start date for ${id}, adjusting end date to match start date`);
							end = new Date(start);
						}

						return {
							id,
							label,
							start,
							end,
							color,
							tooltipData
						};
					};

					// Create bars array with the exact specifications
					const potentialBars = [
						createBar(
							`campaign-${campaign.id}-validity`,
							'Vigência da Campanha',
							campaignStart,
							campaignEnd,
							'#7BC4F7',
							{
								'Taxa': campaign.tax ? `${campaign.tax}%` : 'N/A',
								'Status': campaign.status ? 'Ativo' : 'Inativo',
								'Período': `${campaign.startDate || 'N/A'} a ${campaign.endDate || 'N/A'}`
							}
						),
						createBar(
							`campaign-${campaign.id}-product-${commercialProduct.id}-validity`,
							'Vigência do Produto Comercial',
							productStart,
							productEnd,
							'#8BB455',
							{
								'Produto': commercialProduct.name || 'N/A',
								'Tipo de Operação': commercialProduct.operation_type || 'N/A',
								'Status': commercialProduct.status ? 'Ativo' : 'Inativo',
								'Período': `${commercialProduct.startDate || 'N/A'} a ${commercialProduct.endDate || 'N/A'}`
							}
						),
						createBar(
							`campaign-${campaign.id}-product-${commercialProduct.id}-disbursement`,
							'Período de Desembolso',
							disbursementStart,
							disbursementEnd,
							'#FFB84D',
							{
								'Período': `${commercialProduct.startDisbursementDate || 'N/A'} a ${commercialProduct.endDisbursementDate || 'N/A'}`
							}
						),
						createBar(
							`campaign-${campaign.id}-product-${commercialProduct.id}-due`,
							'Intervalo Vencimento',
							dueStart,
							dueEnd,
							'#F7857F',
							{
								'Período': `${commercialProduct.startDueDate || 'N/A'} a ${commercialProduct.endDueDate || 'N/A'}`
							}
						)
					];

					// Filter out null bars (those with invalid dates)
					const bars = potentialBars.filter(bar => bar !== null);

					if (bars.length === 0) {
						console.warn(`GanttChart: No valid bars created for campaign ${campaign.name} at index ${index}`);
						return null;
					}

					return {
						title: campaign.name,
						bars
					};
				} catch (error) {
					console.error(`GanttChart: Error processing campaign timeline item at index ${index}:`, error, item);
					return null;
				}
			}).filter(group => group !== null && group.bars && group.bars.length > 0);

			return { groups };
		};

		const hasGanttDataError = ref(false);

		const ganttChartData = computed(() => {
			if (!campaignTimeline.value || campaignTimeline.value.length === 0) {
				hasGanttDataError.value = false;
				return null;
			}

			try {
				const result = transformToGanttData(campaignTimeline.value);
				hasGanttDataError.value = false;
				return result;
			} catch (error) {
				console.error('Error transforming campaign timeline data to Gantt format:', error);
				hasGanttDataError.value = true;
				return { groups: [] };
			}
		});


		const commercialProductsForSelect = computed(() =>
			commercialProductsList.value
				?.filter(product => product.enabled === 1)
				?.map(({ id, name }) => ({
					value: id,
					text: name
				})) || []
		);


		const operationTypesForSelect = computed(() =>
			OPERATION_TYPES.map(({ id, name }) => ({
				value: id,
				text: name
			}))
		);


		const isFormValid = computed(() => {
			return formFilters.value.commercial_prd_id !== null &&
			formFilters.value.operation_type !== null &&
			formFilters.value.campaign_status.length > 0;
		});

		const onToggleCampaign = (campaignId: number, value: boolean) => {
			const campaign = campaigns.value.find(c => c.id === campaignId);
			if (campaign) {
				campaign.isActive = value;
			}
		};

		const aplicarFiltros = () => {
			const query = {
				commercial_prd_id: formFilters.value.commercial_prd_id,
				operation_type: formFilters.value.operation_type,
				campaign_status: formFilters.value.campaign_status,
			};

			fetchCampaignTimeline({ query });
		};

		const limparFiltros = () => {
			formFilters.value = {
				commercial_prd_id: null,
				operation_type: null,
				campaign_status: [],
			};
		};


		onBeforeMount(() => {
			fetchCommercialProducts();
		});

		return {
			campaigns,
			formFilters,
			campaignTimeline,
			campaignTimelineRequestStatus,
			commercialProductsList,
			commercialProductsForSelect,
			operationTypesForSelect,
			FORM_STATUSES,
			isFormValid,
			isLoading,
			ganttChartData,
			hasGanttDataError,
			onToggleCampaign,
			aplicarFiltros,
			limparFiltros,
		};
	}
});
</script>

<style lang="scss" scoped>
@import './TabCustomFilter.scss';
</style>

