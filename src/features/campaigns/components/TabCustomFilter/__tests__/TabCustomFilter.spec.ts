import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import TabCustomFilter from '../TabCustomFilter.vue';
import type { CampaignTimelineItem } from '../../../types';

// Mock the composables
vi.mock('../../../composables', () => ({
	useCampaign: () => ({
		campaignTimeline: { value: [] },
		campaignTimelineRequestStatus: { value: 'idle' },
		commercialProductsList: { value: [] },
		commercialProductsListRequestStatus: { value: 'idle' },
		fetchCommercialProducts: vi.fn(),
		fetchCampaignTimeline: vi.fn(),
	})
}));

vi.mock('@/composibles', () => ({
	useIsLoading: () => false
}));

// Mock the GanttChart component
vi.mock('@farm-investimentos/front-mfe-components/src/components/GanttChart/types', () => ({
	GanttData: {}
}));

describe('TabCustomFilter', () => {
	const mockCampaignTimelineData: CampaignTimelineItem[] = [
		{
			campaign: {
				name: 'Test Campaign',
				id: 1,
				status: true,
				startDate: '2025-01-01',
				endDate: '2025-12-31',
				tax: 1.5
			},
			commercialProduct: {
				name: 'Test Product',
				id: 1,
				status: true,
				startDate: '2025-02-01',
				endDate: '2025-11-30',
				operation_type: 1,
				startDueDate: '2025-03-01',
				endDueDate: '2025-10-31',
				startDisbursementDate: '2025-04-01',
				endDisbursementDate: '2025-09-30'
			}
		}
	];

	it('renders without crashing', () => {
		const wrapper = mount(TabCustomFilter);
		expect(wrapper.exists()).toBe(true);
	});

	it('shows placeholder when form is not valid', () => {
		const wrapper = mount(TabCustomFilter);
		expect(wrapper.find('.chart-placeholder').exists()).toBe(true);
		expect(wrapper.text()).toContain('Preencha todos os filtros obrigatórios');
	});

	it('shows empty state when no data is available', async () => {
		const wrapper = mount(TabCustomFilter);
		
		// Set form as valid but no data
		await wrapper.setData({
			formFilters: {
				commercial_prd_id: 1,
				operation_type: 1,
				campaign_status: [1]
			}
		});

		expect(wrapper.find('.chart-empty').exists()).toBe(true);
	});

	it('transforms campaign timeline data correctly', () => {
		const wrapper = mount(TabCustomFilter);
		const vm = wrapper.vm as any;
		
		// Test the transformation function
		const result = vm.transformToGanttData(mockCampaignTimelineData);
		
		expect(result).toBeDefined();
		expect(result.groups).toHaveLength(1);
		expect(result.groups[0].title).toBe('Test Campaign');
		expect(result.groups[0].bars).toHaveLength(4);
		
		// Check bar IDs and colors
		const bars = result.groups[0].bars;
		expect(bars[0].id).toBe('campaign-1-validity');
		expect(bars[0].color).toBe('#7BC4F7');
		expect(bars[1].id).toBe('campaign-1-product-1-validity');
		expect(bars[1].color).toBe('#8BB455');
		expect(bars[2].id).toBe('campaign-1-product-1-disbursement');
		expect(bars[2].color).toBe('#FFB84D');
		expect(bars[3].id).toBe('campaign-1-product-1-due');
		expect(bars[3].color).toBe('#F7857F');
	});

	it('handles invalid data gracefully', () => {
		const wrapper = mount(TabCustomFilter);
		const vm = wrapper.vm as any;
		
		const invalidData = [
			{
				campaign: null,
				commercialProduct: {
					name: 'Test Product',
					id: 1,
					status: true,
					startDate: '2025-02-01',
					endDate: '2025-11-30',
					operation_type: 1,
					startDueDate: '2025-03-01',
					endDueDate: '2025-10-31',
					startDisbursementDate: '2025-04-01',
					endDisbursementDate: '2025-09-30'
				}
			}
		];
		
		const result = vm.transformToGanttData(invalidData);
		expect(result.groups).toHaveLength(0);
	});

	it('handles invalid dates gracefully', () => {
		const wrapper = mount(TabCustomFilter);
		const vm = wrapper.vm as any;
		
		const invalidDateData = [
			{
				campaign: {
					name: 'Test Campaign',
					id: 1,
					status: true,
					startDate: 'invalid-date',
					endDate: '2025-12-31',
					tax: 1.5
				},
				commercialProduct: {
					name: 'Test Product',
					id: 1,
					status: true,
					startDate: '2025-02-01',
					endDate: '2025-11-30',
					operation_type: 1,
					startDueDate: '2025-03-01',
					endDueDate: '2025-10-31',
					startDisbursementDate: '2025-04-01',
					endDisbursementDate: '2025-09-30'
				}
			}
		];
		
		const result = vm.transformToGanttData(invalidDateData);
		// Should still create bars for valid dates
		expect(result.groups).toHaveLength(1);
		expect(result.groups[0].bars.length).toBeLessThan(4); // Some bars filtered out due to invalid dates
	});
});
