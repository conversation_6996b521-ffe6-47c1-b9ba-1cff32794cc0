// Main layout container
.main-layout {
	height: 700px; /* Altura fixa para visualização */
	margin: -24px; /* Compens<PERSON> o padding do container pai em todas as direções */
}

// Sidebar styling
.campaigns-sidebar {
	background-color: #D5E7BB29; /* <PERSON><PERSON>l claro */
	padding: 16px;
	
	height: 100%;

	h3 {
		margin: 0;
		color: #1976D2;
		font-size: 16px;
		font-weight: bold;
	}
}

// Content area layout
.content-area {
	height: 100%;
}

.content-layout {
	height: 100%;
	flex-direction: column;
}

// Filters section styling
.filters-section {

	padding: 16px;

	flex: 0 0 30%; /* Takes 30% of the content area height */

	h3 {
		margin: 0;
		color: #388E3C;
		font-size: 16px;
		font-weight: bold;
	}
}

// Chart section styling
.chart-section {
	padding: 16px;
	flex: 1; /* Takes remaining 70% of the content area height */
	display: flex;
	flex-direction: column;

	h3 {
		margin: 0;
		color: #C2185B;
		font-size: 16px;
		font-weight: bold;
	}

	.chart-container {
		flex: 1;
		min-height: 300px;
		margin-top: 16px;
		border: 1px solid #E0E0E0;
		border-radius: 8px;
		padding: 16px;
		background-color: #FAFAFA;
		overflow: auto;
	}

	.chart-loading,
	.chart-empty,
	.chart-placeholder,
	.chart-error {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 200px;
		margin-top: 16px;
		border: 2px dashed #E0E0E0;
		border-radius: 8px;
		background-color: #F9F9F9;
	}

	.chart-loading {
		border-color: #2196F3;
		background-color: #E3F2FD;
	}

	.chart-empty {
		border-color: #FF9800;
		background-color: #FFF3E0;
	}

	.chart-placeholder {
		border-color: #9E9E9E;
		background-color: #F5F5F5;
	}

	.chart-error {
		border-color: #F44336;
		background-color: #FFEBEE;
	}
}

.filter-title {
	color: var(--farm-primary-darken);
	font-weight: bold;
}

.campaign-card {
	background-color: #ECF6DC;
	border-radius: 5px;
	padding: 16px;
	margin-bottom: 8px;
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.filters-container {
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
	align-items: flex-end; // Changed to flex-end to align buttons with select input
}

.filter-group {
	flex: 1 1 auto;
	min-width: 200px; // Increased minimum width for better content display

	.farm-label {
		display: block;
		margin-bottom: 8px;
	}

	// Ensure select components maintain consistent sizing
	.farm-textfield {
		width: 100%;
		min-width: 100%;

		.farm-textfield--input {
			width: 100%;
			min-width: 100%;
		}

		// Fix dropdown width to match input width
		.farm-contextmenu {
			width: 100%;

			.farm-contextmenu__popup {
				min-width: 100%;
				width: max-content;
				max-width: 400px; // Prevent dropdown from becoming too wide
			}
		}
	}
}

// Estilo específico para o select de Produto Comercial
.commercial-product-group {
	min-width: 280px; // Increased for better content display
	flex: 1.5 1 280px; // Give more space preference to this field

	.commercial-product-select {
		min-width: 100%;
		width: 100%;
	}

	// Specific dropdown width handling for commercial product
	.farm-contextmenu .farm-contextmenu__popup {
		min-width: 280px;
		width: max-content;
		max-width: 450px;
	}
}

// Operation Type select styling
.filter-group:has(.farm-select[id="operation-type"]) {
	min-width: 180px;
	flex: 1 1 180px;
}

// Campaign Status select styling
.filter-group:has(.farm-select[id="campaign-status"]) {
	min-width: 200px;
	flex: 1 1 200px;
}

.buttons-group {
	display: flex;
	gap: 8px;
	flex-shrink: 0;
	align-items: center;
	// Align buttons to the bottom to match the select input height
	align-self: flex-center;
	// Add margin to account for the label height and align with input field
	margin-bottom: 4px; // Matches the margin-bottom from farm-textfield--input

	.farm-btn {
		white-space: nowrap;
		height: 36px; // Match the height of farm-textfield--input
	}
}

// Responsividade para telas menores
@media (max-width: 1024px) {
	.filters-container {
		gap: 8px;
	}

	.filter-group {
		min-width: 180px;
	}

	.commercial-product-group {
		min-width: 250px;
		flex: 1 1 250px;
	}
}

@media (max-width: 768px) {
	.filters-container {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.filter-group {
		min-width: auto;
		width: 100%;
		flex: 1 1 auto;

		// Ensure full width on mobile
		.farm-textfield {
			.farm-contextmenu .farm-contextmenu__popup {
				min-width: 100%;
				width: 100%;
				max-width: none;
			}
		}
	}

	// Mantém largura mínima do produto comercial mesmo em mobile
	.commercial-product-group {
		min-width: 100%;

		.farm-contextmenu .farm-contextmenu__popup {
			min-width: 100%;
			width: 100%;
			max-width: none;
		}
	}

	.buttons-group {
		width: 100%;
		justify-content: flex-start;
		margin-top: 8px;
		margin-bottom: 0; // Reset margin-bottom on mobile
		gap: 8px;
		align-self: stretch; // Reset align-self on mobile

		.farm-btn {
			flex: 1;
			min-width: 120px;
			height: auto; // Reset height on mobile for better touch targets
		}
	}
}

// Additional breakpoint for very small screens
@media (max-width: 480px) {
	.buttons-group {
		flex-direction: column;

		.farm-btn {
			width: 100%;
			min-width: auto;
		}
	}
}

// Global fixes for farm-select dropdown consistency
// These styles ensure dropdown menus match their input field widths
:deep(.farm-contextmenu) {
	position: relative;

	.farm-contextmenu__popup {
		// Ensure dropdown is at least as wide as the input
		min-width: 100%;
		// Use max-content to accommodate longer text while respecting max-width
		width: max-content;
		// Position dropdown properly
		left: 0;
		right: auto;

		// Ensure proper text wrapping in dropdown items
		.farm-listitem {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;

			.farm-caption {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 100%;
			}
		}
	}
}

// Specific handling for select components in filters
.filters-section {
	:deep(.farm-select) {
		.farm-contextmenu {
			.farm-contextmenu__popup {
				// Ensure dropdown doesn't exceed viewport width
				max-width: min(400px, calc(100vw - 32px));

				// Better positioning for mobile
				@media (max-width: 768px) {
					max-width: calc(100vw - 32px);
					left: 0;
					right: 0;
				}
			}
		}
	}
}